# Prometheus Configuration for n8n Docker Stack Monitoring
# This configuration sets up metrics collection for all services in the n8n stack

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'n8n-docker-stack'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'
rule_files:
  - "alert_rules.yml"

# Scrape configuration for monitoring targets
scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: /metrics

  # n8n application metrics (experimental - may not be available)
  - job_name: 'n8n'
    static_configs:
      - targets: ['n8n:5678']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s
    # n8n metrics endpoint - may not be available in all versions

  # PostgreSQL database metrics via postgres_exporter
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    metrics_path: /metrics

  # Node exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    metrics_path: /metrics

  # cAdvisor for container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s
    metrics_path: /metrics

  # Ollama AI service metrics (if available)
  - job_name: 'ollama'
    static_configs:
      - targets: ['ollama:11434']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s
    # Ollama may not expose Prometheus metrics, this is experimental

  # AlertManager metrics
  - job_name: 'alertmanager'
    static_configs:
      - targets: ['alertmanager:9093']
    scrape_interval: 30s
    metrics_path: /metrics

  # Grafana metrics
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    scrape_interval: 30s
    metrics_path: /metrics
