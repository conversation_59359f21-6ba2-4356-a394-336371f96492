# n8n Configuration Environment Variables
# Copy this file to .n8n.env and customize the values for your environment

# Database Configuration
DB_TYPE=postgresdb
DB_POSTGRESDB_HOST=postgres
DB_POSTGRESDB_PORT=5432
DB_POSTGRESDB_DATABASE=n8n
DB_POSTGRESDB_USER=n8n
DB_POSTGRESDB_PASSWORD=n8n_password

# n8n Basic Authentication
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=admin_password

# n8n Host Configuration
N8N_HOST=localhost
N8N_PORT=5678
N8N_PROTOCOL=http
WEBHOOK_URL=http://localhost:5678/

# n8n Execution Settings
EXECUTIONS_PROCESS=main
EXECUTIONS_MODE=regular
EXECUTIONS_TIMEOUT=3600
EXECUTIONS_TIMEOUT_MAX=7200

# n8n Community Packages
N8N_COMMUNITY_PACKAGES_ENABLED=true

# n8n Logging
N8N_LOG_LEVEL=info
N8N_LOG_OUTPUT=console

# n8n Security
N8N_SECURE_COOKIE=false
N8N_ENCRYPTION_KEY=your-encryption-key-here

# n8n Workflow Settings
N8N_DEFAULT_BINARY_DATA_MODE=filesystem
N8N_BINARY_DATA_TTL=24

# n8n Editor Settings
N8N_EDITOR_BASE_URL=http://localhost:5678

# n8n Metrics (optional)
N8N_METRICS=true

# n8n Version Check
N8N_VERSION_NOTIFICATIONS_ENABLED=false

# n8n Telemetry
N8N_DIAGNOSTICS_ENABLED=false
N8N_ANONYMOUS_USAGE_TRACKING=false

# n8n User Management
N8N_USER_MANAGEMENT_DISABLED=true

# n8n Public API
N8N_PUBLIC_API_DISABLED=false

# n8n Templates
N8N_TEMPLATES_ENABLED=true
N8N_TEMPLATES_HOST=https://api.n8n.io/api/

# n8n Personalization
N8N_PERSONALIZATION_ENABLED=false

# n8n Push Connection
N8N_PUSH_BACKEND=websocket

# n8n Queue Mode (for scaling)
QUEUE_BULL_REDIS_HOST=
QUEUE_BULL_REDIS_PORT=
QUEUE_BULL_REDIS_DB=
QUEUE_BULL_REDIS_PASSWORD=

# n8n External Hooks
EXTERNAL_HOOK_FILES=

# n8n Custom CA Certificates
NODE_EXTRA_CA_CERTS=

# n8n Timezone
GENERIC_TIMEZONE=UTC

# n8n Workflow History
N8N_WORKFLOW_HISTORY_ENABLED=true
N8N_WORKFLOW_HISTORY_PRUNE_TIME=168
