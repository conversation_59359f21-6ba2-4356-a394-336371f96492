# AlertManager Configuration for n8n Docker Stack
# This configuration defines how alerts are routed and delivered

global:
  # Global configuration for SMTP (email notifications)
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: ''
  smtp_auth_password: ''
  smtp_require_tls: false

# Templates for alert notifications
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Route configuration - defines how alerts are grouped and routed
route:
  # Default grouping and timing
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default-receiver'

  # Specific routing rules
  routes:
    # Critical alerts get immediate notification
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      repeat_interval: 5m

    # Warning alerts are grouped and sent less frequently
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_wait: 30s
      repeat_interval: 30m

    # Database-specific alerts
    - match_re:
        alertname: 'PostgreSQL.*'
      receiver: 'database-alerts'

    # n8n service alerts
    - match:
        job: n8n
      receiver: 'n8n-alerts'

# Inhibition rules - suppress certain alerts when others are firing
inhibit_rules:
  # Suppress warning alerts when critical alerts are firing for the same service
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance']

# Receiver configurations - define how notifications are sent
receivers:
  # Default receiver for unmatched alerts
  - name: 'default-receiver'
    webhook_configs:
      - url: 'http://localhost:9093/api/v1/alerts'
        send_resolved: true

  # Critical alerts - immediate notification via multiple channels
  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 CRITICAL: {{ .GroupLabels.alertname }} - {{ .GroupLabels.instance }}'
        body: |
          Alert: {{ .GroupLabels.alertname }}
          Instance: {{ .GroupLabels.instance }}
          Severity: {{ .CommonLabels.severity }}
          
          {{ range .Alerts }}
          Description: {{ .Annotations.description }}
          {{ end }}
          
          Time: {{ .CommonAnnotations.timestamp }}
        headers:
          Priority: 'high'
    
    webhook_configs:
      - url: 'http://localhost:9093/api/v1/alerts'
        send_resolved: true
        title: 'Critical Alert: {{ .GroupLabels.alertname }}'

  # Warning alerts - grouped notifications
  - name: 'warning-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '⚠️  WARNING: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Labels.alertname }}
          Instance: {{ .Labels.instance }}
          Description: {{ .Annotations.description }}
          ---
          {{ end }}

  # Database-specific alerts
  - name: 'database-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🗄️  Database Alert: {{ .GroupLabels.alertname }}'
        body: |
          Database Alert Details:
          
          {{ range .Alerts }}
          Alert: {{ .Labels.alertname }}
          Database: {{ .Labels.datname }}
          Instance: {{ .Labels.instance }}
          Description: {{ .Annotations.description }}
          {{ end }}

  # n8n service alerts
  - name: 'n8n-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🔄 n8n Service Alert: {{ .GroupLabels.alertname }}'
        body: |
          n8n Service Alert:
          
          {{ range .Alerts }}
          Alert: {{ .Labels.alertname }}
          Service: {{ .Labels.job }}
          Instance: {{ .Labels.instance }}
          Description: {{ .Annotations.description }}
          {{ end }}
    
    webhook_configs:
      - url: 'http://n8n:5678/webhook/monitoring-alert'
        send_resolved: true
        title: 'n8n Service Alert'
