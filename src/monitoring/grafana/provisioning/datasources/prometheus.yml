# Grafana Data Source Provisioning for n8n Docker Stack
# This configuration automatically sets up Prometheus as a data source

apiVersion: 1

# List of data sources to insert/update depending on what's available in the database
datasources:
  # Prometheus data source for metrics
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "15s"
      queryTimeout: "60s"
      httpMethod: "POST"
      manageAlerts: true
      alertmanagerUid: "alertmanager"
    version: 1

  # AlertManager data source for alert management
  - name: AlertManager
    type: alertmanager
    access: proxy
    url: http://alertmanager:9093
    uid: "alertmanager"
    editable: true
    jsonData:
      implementation: "prometheus"
    version: 1
